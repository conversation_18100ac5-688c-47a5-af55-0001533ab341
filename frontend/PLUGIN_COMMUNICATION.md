# 插件通信系统使用文档

## 概述

本插件通信系统实现了插件应用与主应用之间的双向通信机制，支持：

1. 通过URL参数获取插件唯一标识
2. 基于BroadcastChannel的实时通信
3. 插件启动时的Hello握手机制
4. 动态配置的HTTP API实例
5. 便捷的数据交换API

## 系统架构

```
主应用 ←→ BroadcastChannel ←→ 插件应用
                ↓
            HTTP API (基于主应用配置)
```

## 使用方法

### 1. 启动插件

插件应用需要通过URL参数传入插件ID：

```
http://localhost:9000/?pluginId=abc123-def456-ghi789
```

支持的URL参数名：
- `pluginId` (推荐)
- `channel`
- `id`

### 2. 在组件中使用

#### 基础用法

```vue
<script setup>
import { usePluginStore } from '@/stores/plugin.js'
import pluginApi from '@/utils/pluginApi.js'

const pluginStore = usePluginStore()

// 检查插件是否就绪
if (pluginStore.isReady) {
  console.log('插件已就绪')
}
</script>
```

#### 发送数据到主应用

```javascript
// 发送简单数据
pluginApi.sendToHost('modelData', {
  nodes: [...],
  edges: [...]
})

// 通知状态变化
pluginApi.notifyStatusChange('editing', { 
  currentTool: 'select' 
})

// 通知数据变化
pluginApi.notifyDataChange('model', modelData)
```

#### 请求主应用数据

```javascript
// 请求用户信息
const userInfo = await pluginApi.getUserInfo()

// 请求项目信息
const projectInfo = await pluginApi.getProjectInfo()

// 自定义数据请求
const customData = await pluginApi.requestFromHost('customType', {
  param1: 'value1'
})
```

#### 文件操作

```javascript
// 请求打开文件
const fileData = await pluginApi.requestOpenFile('json', {
  title: '选择模型文件',
  filters: ['.json', '.xml']
})

// 请求保存文件
await pluginApi.requestSaveFile(modelData, 'model.json', 'json')
```

#### HTTP API调用

```javascript
// GET请求
const data = await pluginApi.get('/api/models')

// POST请求
const result = await pluginApi.post('/api/models', {
  name: 'New Model',
  data: modelData
})

// 文件上传
await pluginApi.uploadFile('/api/upload', file, (progress) => {
  console.log(`上传进度: ${progress}%`)
})

// 文件下载
await pluginApi.downloadFile('/api/download/model.json', 'model.json')
```

### 3. 监听主应用消息

```javascript
import pluginCommunication from '@/utils/pluginCommunication.js'

// 监听自定义消息
pluginCommunication.onMessage('customMessage', (message) => {
  console.log('收到自定义消息:', message.data)
})

// 监听配置更新
pluginCommunication.onMessage('configUpdate', (message) => {
  console.log('配置已更新:', message.data)
})
```

## API参考

### PluginStore (Pinia)

#### 状态
- `isInitialized`: 是否已初始化
- `isConnected`: 是否已连接
- `config`: 主应用配置
- `pluginId`: 插件ID
- `error`: 错误信息
- `loading`: 加载状态

#### 计算属性
- `isReady`: 插件是否就绪
- `apiBase`: API基础URL
- `token`: 认证token
- `hasToken`: 是否有token

#### 方法
- `initialize()`: 初始化插件
- `sendData(type, data)`: 发送数据
- `requestData(type, params)`: 请求数据
- `sendMessage(message)`: 发送消息

## 消息格式

### Hello消息 (插件 → 主应用)

```javascript
{
  type: 'hello',
  from: 'plugin',
  pluginId: 'abc123-def456-ghi789',
  timestamp: 1640995200000,
  data: {
    name: 'lantu-plugin-modeler',
    pluginId: 'abc123-def456-ghi789'
  }
}
```

### 配置响应 (主应用 → 插件)

```javascript
{
  type: 'config',
  to: 'plugin',
  timestamp: 1640995200000,
  data: {
    apiBase: 'http://localhost:8080/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    // 其他配置...
  }
}
```

### 数据消息 (插件 → 主应用)

```javascript
{
  type: 'data',
  from: 'plugin',
  pluginId: 'abc123-def456-ghi789',
  timestamp: 1640995200000,
  dataType: 'modelData',
  data: {
    // 具体数据...
  }
}
```

## 错误处理

系统提供了完善的错误处理机制：

1. **初始化错误**: 插件ID缺失、通信建立失败等
2. **通信错误**: 消息发送失败、超时等
3. **API错误**: HTTP请求失败、认证失败等

错误信息会记录在`pluginStore.error`中，并在控制台输出详细日志。

## 注意事项

1. 确保URL中包含有效的插件ID参数
2. 在使用API前检查`pluginStore.isReady`状态
3. 所有异步操作都应该进行错误处理
4. 插件卸载时会自动清理通信资源

## 示例组件

参考 `src/components/PluginExample.vue` 查看完整的使用示例。
