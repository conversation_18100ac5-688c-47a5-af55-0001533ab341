<template>
  <div id="canvas-container" class="canvas-container">
    <div id="canvas" class="canvas"></div>

    <!-- 右键菜单 -->
    <q-menu
      v-model="editorStore.contextMenu.visible"
      :target="true"
      context-menu
      anchor="top left"
      self="top left"
      auto-close
    >
      <q-list dense style="min-width: 160px">
        <!-- 节点右键菜单 -->
        <template v-if="editorStore.contextMenu.targetType === 'node'">
          <q-item clickable v-close-popup @click="renameNode" v-if="editorStore.contextMenu.selectedCells.length === 1">
            <q-item-section avatar>
              <q-icon name="edit" />
            </q-item-section>
            <q-item-section>重命名</q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="editNodeProperties" v-if="editorStore.contextMenu.selectedCells.length === 1">
            <q-item-section avatar>
              <q-icon name="settings" />
            </q-item-section>
            <q-item-section>编辑属性</q-item-section>
          </q-item>
          <q-separator />
          <q-item clickable v-close-popup @click="cutSelection">
            <q-item-section avatar>
              <q-icon name="content_cut" />
            </q-item-section>
            <q-item-section>剪切</q-item-section>
            <q-item-section side>
              <q-item-label caption>Ctrl+X</q-item-label>
            </q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="copySelection">
            <q-item-section avatar>
              <q-icon name="content_copy" />
            </q-item-section>
            <q-item-section>复制</q-item-section>
            <q-item-section side>
              <q-item-label caption>Ctrl+C</q-item-label>
            </q-item-section>
          </q-item>
          <q-separator />
          <q-item clickable v-close-popup @click="deleteSelection">
            <q-item-section avatar>
              <q-icon name="delete" />
            </q-item-section>
            <q-item-section>删除节点</q-item-section>
            <q-item-section side>
              <q-item-label caption>Del</q-item-label>
            </q-item-section>
          </q-item>
        </template>

        <!-- 连线右键菜单 -->
        <template v-if="editorStore.contextMenu.targetType === 'edge'">
          <q-item clickable v-close-popup @click="editEdgeProperties">
            <q-item-section avatar>
              <q-icon name="settings" />
            </q-item-section>
            <q-item-section>编辑属性</q-item-section>
          </q-item>
          <q-separator />
          <q-item clickable v-close-popup @click="deleteSelection">
            <q-item-section avatar>
              <q-icon name="delete" />
            </q-item-section>
            <q-item-section>删除连线</q-item-section>
            <q-item-section side>
              <q-item-label caption>Del</q-item-label>
            </q-item-section>
          </q-item>
        </template>

        <!-- 空白区域右键菜单 -->
        <template v-if="editorStore.contextMenu.targetType === 'blank'">
          <q-item
            clickable
            v-close-popup
            @click="pasteFromClipboard"
            :disable="graph.isClipboardEmpty()"
          >
            <q-item-section avatar>
              <q-icon name="content_paste" />
            </q-item-section>
            <q-item-section>粘贴</q-item-section>
            <q-item-section side>
              <q-item-label caption>Ctrl+V</q-item-label>
            </q-item-section>
          </q-item>
          <q-separator />
          <q-item clickable v-close-popup @click="selectAllNodes">
            <q-item-section avatar>
              <q-icon name="select_all" />
            </q-item-section>
            <q-item-section>全选</q-item-section>
            <q-item-section side>
              <q-item-label caption>Ctrl+A</q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </q-list>
    </q-menu>
  </div>
</template>

<script setup>
import {onMounted, onUnmounted, ref, watch, computed} from 'vue'
import {Graph, Path, Polyline} from '@antv/x6'
import {Keyboard} from '@antv/x6-plugin-keyboard'
import {History} from '@antv/x6-plugin-history'
import {Clipboard} from '@antv/x6-plugin-clipboard'
import {Selection} from '@antv/x6-plugin-selection'
import {Snapline} from '@antv/x6-plugin-snapline'
import {useEditorStore} from 'stores/editor.js'
import {portManager} from 'src/utils/port-manager.js'
import {ConnectionConfig, getConnectionTypeName, getModuleTypeName, validateConnection} from 'src/utils/modules.js'

const $q = useQuasar();

const editorStore = useEditorStore()

const graph = ref(null)

// 注册自定义平行边连接器
const registerParallelConnector = () => {
  /**
   * 自定义平行边连接器
   * @param sourcePoint 源节点连接点
   * @param targetPoint 目标节点连接点
   * @param routePoints 路径点
   * @param options 额外选项，可以从中获取边实例和图实例
   * @returns SVG 路径字符串
   */
  const parallelConnector = function (
    sourcePoint,
    targetPoint,
    routePoints,
    options
  ) {
    // 从选项中获取边视图，进而获取图和边模型
    const edgeView = this;
    const graph = edgeView.graph;
    const edge = edgeView.cell;

    // 平行边的间距
    const gap = options.gap || 10;

    // 获取边的源节点和目标节点
    const sourceNode = edge.getSourceNode();
    const targetNode = edge.getTargetNode();

    // ★ 修正点 1: 当节点不存在时，手动创建一条直线路径作为后备
    if (!sourceNode || !targetNode) {
      const points = [sourcePoint, ...routePoints, targetPoint];
      const polyline = new Polyline(points);
      const path = new Path(polyline);
      return options.raw ? path : path.serialize();
    }

    // 获取所有连接到这两个节点的边
    const parallelEdges = graph
      .getConnectedEdges(sourceNode)
      .filter((e) => {
        return (
          e.getConnector() === "parallel" &&
          ((e.getSourceNode()?.id === sourceNode.id &&
              e.getTargetNode()?.id === targetNode.id) ||
            (e.getSourceNode()?.id === targetNode.id &&
              e.getTargetNode()?.id === sourceNode.id))
        );
      });

    // 在平行边中找到当前边的索引
    const edgeIndex = parallelEdges.findIndex((e) => e.id === edge.id);
    const totalEdges = parallelEdges.length;

    // 如果只有一条边，或者找不到当前边，则按直线渲染
    if (totalEdges <= 1 || edgeIndex === -1 || routePoints.length > 0) {
      const points = [sourcePoint, ...routePoints, targetPoint];
      const polyline = new Polyline(points);
      const path = new Path(polyline);
      return options.raw ? path : path.serialize();
    }

    // 计算偏移量
    // 矢量 (dx, dy) 是从源点到目标点的向量
    const dx = targetPoint.x - sourcePoint.x;
    const dy = targetPoint.y - sourcePoint.y;

    // 计算向量的长度
    const len = Math.sqrt(dx * dx + dy * dy);

    // 计算偏移的中心位置
    const centerIndex = (totalEdges - 1) / 2;
    const offsetIndex = edgeIndex - centerIndex;

    // 计算总偏移量
    const offset = offsetIndex * gap;

    // 计算垂直于 (dx, dy) 的法线向量 (nx, ny)
    // 这是通过将向量 (dx, dy) 旋转 90 度得到的
    const nx = -dy / len;
    const ny = dx / len;

    // 应用偏移量，计算新的起点和终点
    const newSourceX = sourcePoint.x + offset * nx;
    const newSourceY = sourcePoint.y + offset * ny;
    const newTargetX = targetPoint.x + offset * nx;
    const newTargetY = targetPoint.y + offset * ny;

    // 使用新的点生成路径
    const polylien = new Polyline([
      {x: newSourceX, y: newSourceY},
      ...routePoints,
      {x: newTargetX, y: newTargetY},
    ]);
    const path = new Path(polylien);

    path.moveTo(newSourceX, newSourceY);
    path.lineTo(newTargetX, newTargetY);

    return path.serialize();
  };

  Graph.registerConnector('parallel', parallelConnector, true)
}

// 初始化图形编辑器
const initializeGraph = async () => {
  try {
    // 注册自定义连接器
    registerParallelConnector()

    // 创建图形编辑器实例
    graph.value = new Graph({
      container: document.getElementById("canvas"),
      autoResize: true,
      panning: true,
      mousewheel: {
        enabled: true,
        modifiers: ["ctrl"],
        minScale: 0.3,
        maxScale: 3,
      },
      grid: {
        visible: true,
        type: "mesh",
        size: 1,
        args: {
          color: "#e0e0e0",
          thickness: 1,
        },
      },
      connecting: {
        allowBlank: false,
        allowLoop: false,
        allowNode: true,
        allowEdge: false,
        allowMulti: true,
        highlight: true,
        snap: {radius: 20},
        router: {name: "normal"},
        createEdge() {
          const typeConfig = ConnectionConfig[editorStore.connectionMode]

          return graph.value.createEdge({
            shape: "edge",
            connector: 'parallel',
            attrs: {
              line: {
                stroke: typeConfig.color,
                strokeWidth: typeConfig.width,
                strokeDasharray: typeConfig.strokeDasharray,
                targetMarker: {
                  name: "classic",
                  size: 8,
                  fill: typeConfig.color,
                  stroke: typeConfig.color,
                },
              },
            },
            zIndex: 0,
            data: {
              type: 'packet_stream',
              portIndices: {
                srcIdx: -1,
                dstIdx: -1,
              },
            }
          })
        },
        validateConnection: ({sourceCell, targetCell}) => {
          const {valid} = validateConnection(sourceCell, targetCell, editorStore.connectionMode)
          return valid;
        },
      },
      resizing: true,
      rotating: false,
      selecting: {
        enabled: true,
        rubberband: true,
        movable: true,
        showNodeSelectionBox: true,
      },
      snapline: true,
      keyboard: true,
      clipboard: true,
      history: true,
    })

    // 使用插件
    graph.value
      .use(new Keyboard({enabled: true, global: true}))
      .use(new History({enabled: true}))
      .use(new Clipboard({enabled: true}))
      .use(new Selection({
        enabled: true,
        multiple: true,
        rubberband: true,
        movable: true,
        showNodeSelectionBox: true,
        modifiers: ["ctrl"],
      }))
      .use(new Snapline({enabled: true}))

    // 绑定事件
    bindEvents()

    // 设置键盘快捷键
    setupKeyboardShortcuts()

    editorStore.statusMessage = '编辑器初始化完成';
    console.log("✅ 编辑器初始化成功")

    return true
  } catch (error) {
    console.error("❌ 编辑器初始化失败:", error)
    editorStore.statusMessage = '编辑器初始化失败: ' + error.message;
    return false
  }
}

// 绑定图形编辑器事件
const bindEvents = () => {
  if (!graph.value) return

  // 节点双击事件 - 编辑属性
  graph.value.on('node:dblclick', ({node}) => {
    editNodeAttributes(node)
  })

  // 边双击事件 - 编辑连接属性
  graph.value.on('edge:dblclick', ({edge}) => {
    editEdgeProperties(edge)
  })

  // 画布点击事件 - 清除选择
  graph.value.on('blank:click', () => {
    graph.value.cleanSelection()
    editorStore.contextMenu.visible = false
  })

  // 右键菜单事件
  graph.value.on('node:contextmenu', ({e, node}) => {
    e.preventDefault()
    showContextMenu(e, 'node', node)
  })

  graph.value.on('edge:contextmenu', ({e, edge}) => {
    e.preventDefault()
    showContextMenu(e, 'edge', edge)
  })

  graph.value.on('blank:contextmenu', ({e}) => {
    e.preventDefault()
    showContextMenu(e, 'blank', null)
  })

  // 缩放事件 - 更新缩放级别显示
  graph.value.on('scale', ({sx}) => {
    editorStore.zoomLevel = Math.round(sx * 100)
  })

  // 历史记录变化事件 - 更新撤销重做状态
  graph.value.on('history:change', () => {
    editorStore.canUndo = graph.value.canUndo()
    editorStore.canRedo = graph.value.canRedo()
  })

  // 图形变化事件 - 更新统计信息和保存状态
  graph.value.on('cell:added', () => {
    updateStats()
    editorStore.hasUnsavedChanges = true;
  })

  graph.value.on('cell:removed', () => {
    updateStats()
    editorStore.hasUnsavedChanges = true;
  })

  // 连接创建事件
  graph.value.on('edge:connected', ({isNew, edge, currentCell}) => {
    if (isNew) {
      const success = portManager.autoAssignPortIndices(edge)
      if (!success) {
        graph.value.removeEdge(edge)
        editorStore.statusMessage = '无可用端口，请检查模块连接或配置端口索引'
        return
      }
      // 更新端口管理器缓存
      portManager.updatePortUsageCache(graph.value)
      editorStore.hasUnsavedChanges = true;
    }
    const pos = currentCell.getProp("position")
    setTimeout(() => {
      graph.value.disableHistory()
      currentCell.setProp("position", {x: 0, y: 0}, {silent: true})
      currentCell.setProp("position", pos)
      graph.value.enableHistory()
    }, 0);

  })

  console.log("🔗 事件绑定完成")
}

// 设置键盘快捷键
const setupKeyboardShortcuts = () => {
  if (!graph.value) return

  // 撤销/重做
  graph.value.bindKey(['ctrl+z', 'cmd+z'], () => {
    if (graph.value.canUndo()) {
      graph.value.undo()
    }
    return false
  })

  graph.value.bindKey(['ctrl+y', 'cmd+y', 'ctrl+shift+z'], () => {
    if (graph.value.canRedo()) {
      graph.value.redo()
    }
    return false
  })

  // 复制/粘贴
  graph.value.bindKey(['ctrl+c', 'cmd+c'], () => {
    const cells = graph.value.getSelectedCells()
    if (cells.length) {
      graph.value.copy(cells)
    }
    return false
  })

  graph.value.bindKey(['ctrl+v', 'cmd+v'], () => {
    if (!graph.value.isClipboardEmpty()) {
      const cells = graph.value.paste({offset: 32})
      graph.value.cleanSelection()
      graph.value.select(cells)
    }
    return false
  })

  graph.value.bindKey(['ctrl+x', 'cmd+x'], () => {
    const cells = graph.value.getSelectedCells()
    if (cells.length) {
      graph.value.copy(cells)
      graph.value.removeCells(cells)
    }
    return false
  })

  // 删除
  graph.value.bindKey(['del', 'backspace'], () => {
    const cells = graph.value.getSelectedCells()
    if (cells.length) {
      graph.value.removeCells(cells)
    }
    return false
  })

  // 全选
  graph.value.bindKey(['ctrl+a', 'cmd+a'], () => {
    const nodes = graph.value.getNodes()
    if (nodes.length) {
      graph.value.select(nodes)
    }
    return false
  })

  console.log("⌨️ 键盘快捷键设置完成")
}

// 编辑节点属性
const editNodeAttributes = (node) => {
  const nodeData = JSON.parse(JSON.stringify(node.getData())) || {};
  editorStore.attributeDialog.title = `${getModuleTypeName(nodeData.type)} 属性`;
  editorStore.attributeDialog.data = nodeData;
  editorStore.attributeDialog.node = node;
  editorStore.attributeDialog.visible = true;
}

// 编辑边属性
const editEdgeProperties = (edge) => {
  // 实现边属性编辑逻辑
  editorStore.connectionDialog.edge = edge;
  editorStore.connectionDialog.data = JSON.parse(JSON.stringify(edge.getData()) || {});
  editorStore.connectionDialog.title = `${getConnectionTypeName(editorStore.connectionDialog.data.type)} 属性`;
  editorStore.connectionDialog.availableSourcePorts = portManager.getAvailablePorts(
    edge.getSourceNode(), 'output', editorStore.connectionDialog.data.type
  );

  editorStore.connectionDialog.availableTargetPorts = portManager.getAvailablePorts(
    edge.getTargetNode(), 'input', editorStore.connectionDialog.data.type
  );

  editorStore.connectionDialog.visible = true;
}

// 显示右键菜单
const showContextMenu = (e, targetType, targetCell) => {
  editorStore.contextMenu.visible = false
  editorStore.contextMenu.targetType = targetType
  editorStore.contextMenu.targetCell = targetCell

  // 右键逻辑：如果点击的是已经选中的元素，保持选中不变弹出菜单
  //         如果点击的是未选中的元素，清空其他选中，选中该元素并弹出菜单
  if (targetCell && !graph.value.isSelected(targetCell)) {
    graph.value.cleanSelection()
    graph.value.select(targetCell)
  }

  editorStore.contextMenu.selectedCells = graph.value.getSelectedCells()

  // 延迟显示菜单，确保位置计算正确
  setTimeout(() => {
    editorStore.contextMenu.visible = true
    console.log("🎯 右键菜单显示成功", editorStore.contextMenu)
  }, 10)
}


const generateUniqueModuleName = (graph, type) => {
  const nodes = graph.getNodes();
  const existingNames = nodes
    .map((node) => node.getData()?.name || "")
    .filter((name) => name.startsWith(type));

  let index = 1;
  let newName = `${type}_${index}`;

  while (existingNames.includes(newName)) {
    index++;
    newName = `${type}_${index}`;
  }

  return newName;
}

// 右键菜单操作方法
const renameNode = () => {
  const node = editorStore.contextMenu.targetCell
  if (!node) return

  const currentName = node.getData()?.name || ''
  $q.dialog({
    title: '输入',
    message: '请输入新的节点名称:',
    prompt: {
      model: currentName,
      type: 'text'
    },
    cancel: true,
    persistent: true
  }).onOk(newName => {
    if (newName && newName !== currentName) {
      // 检查名称是否已存在
      const existingNames = graph.value.getNodes()
        .filter(n => n.id !== node.id)
        .map(n => n.getData()?.name || '')

      if (existingNames.includes(newName)) {
        alert('节点名称已存在，请使用其他名称')
        return
      }

      // 更新节点名称
      const nodeData = node.getData() || {}
      nodeData.name = newName
      node.setData(nodeData)
      node.setAttrs({
        label: {
          text: newName
        }
      })

      editorStore.hasUnsavedChanges = true;
    }
  });

}

const editNodeProperties = () => {
  const node = editorStore.contextMenu.targetCell
  if (node) {
    editNodeAttributes(node)
  }
}

const cutSelection = () => {
  if (graph.value) {
    const cells = graph.value.getSelectedCells()
    if (cells.length) {
      graph.value.copy(cells)
      graph.value.removeCells(cells)
    }
  }
}

const copySelection = () => {
  if (graph.value) {
    const cells = graph.value.getSelectedCells()
    if (cells.length) {
      graph.value.copy(cells)
    }
  }
}

const deleteSelection = () => {
  if (graph.value) {
    const cells = graph.value.getSelectedCells()
    if (cells.length) {
      graph.value.removeCells(cells)
    }
  }
}

const pasteFromClipboard = () => {
  if (graph.value && !graph.value.isClipboardEmpty()) {
    const cells = graph.value.paste({offset: 32})
    graph.value.cleanSelection()
    graph.value.select(cells)
  }
}

const selectAllNodes = () => {
  if (graph.value) {
    const nodes = graph.value.getNodes()
    if (nodes.length) {
      graph.value.select(nodes)
    }
  }
}


// // 更新边的样式
// const updateEdgeStyle = (edge) => {
//   const edgeData = edge.getData() || {}
//   const connectionType = edgeData.type || 'packet_stream'
//
//   let style = {
//     line: {
//       stroke: '#1890ff',
//       strokeWidth: 2,
//       targetMarker: {
//         name: 'classic',
//         size: 8,
//       },
//     },
//   }
//
//   if (connectionType === 'statistics_wire') {
//     style.line.stroke = '#52c41a'
//     style.line.strokeWidth = 1
//     style.line.strokeDasharray = '5,5'
//   }
//
//   edge.setAttrs(style)
// }

// 更新统计信息
const updateStats = () => {
  if (!graph.value) return

  const nodes = graph.value.getNodes()
  const edges = graph.value.getEdges()

  editorStore.stats.moduleCount = nodes.length;
  editorStore.stats.connectionCount = edges.length;
}

// 暴露给父组件的方法
defineExpose({
  // 添加模块到画布
  addModule: (moduleType, position = null) => {
    if (!graph.value) return null

    // 默认位置
    const defaultPosition = position || {
      x: Math.random() * 400 + 100,
      y: Math.random() * 300 + 100
    }

    const name = generateUniqueModuleName(graph.value, moduleType);

    // 创建节点
    const node = graph.value.addNode({
      shape: 'image',
      x: defaultPosition.x,
      y: defaultPosition.y,
      width: 64,
      height: 64,
      imageUrl: `/images/modules/${moduleType}.png`,
      label: name,
      attrs: {
        image: {
          magnet: true,
        },
        label: {
          refX: 0.5,
          refY: "100%",
          refY2: 4,
          textAnchor: "middle",
          textVerticalAnchor: "top",
        },
      },
      data: {
        type: moduleType,
        name: name,
        attributeValues: {},
      },
    })

    editorStore.hasUnsavedChanges = true;
    return node
  },

  // 缩放操作
  zoomIn: () => {
    if (graph.value) {
      graph.value.zoom(0.1)
    }
  },

  zoomOut: () => {
    if (graph.value) {
      graph.value.zoom(-0.1)
    }
  },

  zoomFit: () => {
    if (graph.value) {
      graph.value.zoomToFit({padding: 20})
    }
  },

  // 编辑操作
  undo: () => {
    if (graph.value && graph.value.canUndo()) {
      graph.value.undo()
    }
  },

  redo: () => {
    if (graph.value && graph.value.canRedo()) {
      graph.value.redo()
    }
  },

  cut: () => {
    if (graph.value) {
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.copy(cells)
        graph.value.removeCells(cells)
      }
    }
  },

  copy: () => {
    if (graph.value) {
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.copy(cells)
      }
    }
  },

  paste: () => {
    if (graph.value && !graph.value.isClipboardEmpty()) {
      const cells = graph.value.paste({offset: 32})
      graph.value.cleanSelection()
      graph.value.select(cells)
    }
  },

  selectAll: () => {
    if (graph.value) {
      const nodes = graph.value.getNodes()
      if (nodes.length) {
        graph.value.select(nodes)
      }
    }
  },

  deleteSelected: () => {
    if (graph.value) {
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.removeCells(cells)
      }
    }
  },

  // 文件操作
  newModel: () => {
    if (graph.value) {
      graph.value.clearCells()
      editorStore.hasUnsavedChanges = false;
      editorStore.statusMessage = '新建模型';
    }
  },

  // 获取模型数据
  getModelData: () => {
    if (!graph.value) return null

    return {
      nodes: graph.value.getNodes().map(node => ({
        id: node.id,
        position: node.getPosition(),
        size: node.getSize(),
        data: node.getData(),
        attrs: node.getAttrs(),
      })),
      edges: graph.value.getEdges().map(edge => ({
        id: edge.id,
        source: edge.getSource(),
        target: edge.getTarget(),
        data: edge.getData(),
        attrs: edge.getAttrs(),
      })),
      metadata: {
        version: '1.0',
        created: new Date().toISOString(),
        modified: new Date().toISOString(),
      }
    }
  },

  // 加载模型数据
  loadModelData: (modelData) => {
    if (!graph.value || !modelData) return false

    try {
      graph.value.clearCells()

      // 加载节点
      if (modelData.nodes) {
        modelData.nodes.forEach(nodeData => {
          graph.value.addNode({
            id: nodeData.id,
            x: nodeData.position.x,
            y: nodeData.position.y,
            width: nodeData.size.width,
            height: nodeData.size.height,
            attrs: nodeData.attrs,
            data: nodeData.data,
          })
        })
      }

      // 加载边
      if (modelData.edges) {
        modelData.edges.forEach(edgeData => {
          graph.value.addEdge({
            id: edgeData.id,
            source: edgeData.source,
            target: edgeData.target,
            attrs: edgeData.attrs,
            data: edgeData.data,
          })
        })
      }

      editorStore.hasUnsavedChanges = false;
      editorStore.statusMessage = '模型加载完成'
      return true
    } catch (error) {
      console.error('加载模型数据失败:', error)
      editorStore.statusMessage = '模型加载失败: ' + error.message;
      return false
    }
  }
})

// 监听连接模式变化
watch(() => editorStore.connectionMode, (newMode) => {
  console.log('连接模式已切换为:', newMode)
})

// 全局点击事件处理器
const handleGlobalClick = (e) => {
  // 如果点击的不是右键菜单，则隐藏菜单
  if (!e.target.closest('.q-menu')) {
    editorStore.contextMenu.visible = false
  }
}

onMounted(async () => {
  // 初始化编辑器
  const success = await initializeGraph()
  if (success) {
    console.log('✅ 编辑器初始化成功')
  } else {
    console.error('❌ 编辑器初始化失败')
  }

  // 添加全局点击事件监听器
  document.addEventListener('click', handleGlobalClick)
})

onUnmounted(() => {
  // 移除全局点击事件监听器
  document.removeEventListener('click', handleGlobalClick)

  if (graph.value) {
    graph.value.dispose()
    graph.value = null
  }
  console.log("🗑️ 编辑器已销毁")
})
</script>

<style lang="scss" scoped>
.canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #f8f9fa;

  .canvas {
    width: 100%;
    height: 100%;
  }
}

// 右键菜单样式优化
:deep(.q-menu) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 6px;

  .q-item {
    min-height: 32px;
    padding: 4px 12px;

    &:hover {
      background-color: #f5f5f5;
    }

    .q-item__section--avatar {
      min-width: 24px;
      padding-right: 8px;

      .q-icon {
        font-size: 16px;
        color: #666;
      }
    }

    .q-item__section--side {
      .q-item__label--caption {
        color: #999;
        font-size: 11px;
      }
    }
  }

  .q-separator {
    margin: 4px 0;
  }
}
</style>
