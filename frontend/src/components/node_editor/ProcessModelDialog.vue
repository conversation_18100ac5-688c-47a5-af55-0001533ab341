<template>
  <q-dialog
    v-model="editorStore.processModelDialog.visible"
    persistent
  >
    <q-card class="process-model-dialog">
      <q-card-section class="dialog-header">
        <div class="dialog-title">
          <q-icon name="layers" />
          <span>选择进程模型</span>
        </div>
        <q-btn
          flat
          dense
          round
          icon="close"
          @click="closeProcessModelDialog"
        />
      </q-card-section>

      <q-card-section class="dialog-body">
        <q-select
          v-model="editorStore.processModelDialog.selectedModel"
          :options="processModelOptions"
          label="进程模型"
          outlined
          dense
          emit-value
          map-options
          clearable
          class="form-field"
        />

        <div class="model-description">
          <div v-if="editorStore.processModelDialog.selectedModel">
            {{ editorStore.getProcessModelDescription(editorStore.processModelDialog.selectedModel) }}
          </div>
          <div v-else>
            请选择适合的进程模型，或保持为空以使用基础模块功能。
          </div>
        </div>
      </q-card-section>

      <q-card-actions class="dialog-footer" align="right">
        <q-btn
          flat
          label="取消"
          @click="closeProcessModelDialog"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="applyProcessModel"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useEditorStore } from 'stores/editor'
import { modelManager, DEVICE } from 'src/utils/modelManager.js'

const editorStore = useEditorStore()

// 可用的进程模型列表
const availableModels = ref([])

const processModelOptions = computed(() => {
  return availableModels.value.map(model => {
    const modelName = modelManager.removeModelSuffix(model.name)
    return {
      label: `${modelName} (${model.visibility})`,
      value: modelName
    }
  })
})

// 监听对话框打开，加载可用的进程模型列表
watch(() => editorStore.processModelDialog.visible, async (visible) => {
  if (visible) {
    try {
      availableModels.value = await modelManager.listModel(DEVICE)
    } catch (error) {
      console.error('加载进程模型列表失败:', error)
      availableModels.value = []
    }
  }
})

const closeProcessModelDialog = () => {
  editorStore.closeProcessModelDialog()
}

const applyProcessModel = () => {
  // TODO: 实现应用进程模型
  console.log('应用进程模型:', editorStore.processModelDialog.selectedModel)

  // 更新属性对话框中的模型信息
  if (editorStore.attributeDialog.data) {
    editorStore.attributeDialog.data.model = editorStore.processModelDialog.selectedModel
  }

  closeProcessModelDialog()
}
</script>

<style lang="scss" scoped>
.process-model-dialog {
  width: 400px;
  max-width: 90vw;

  .dialog-header {
    padding: 16px 24px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;

    .dialog-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .dialog-body {
    padding: 24px;

    .form-field {
      margin-bottom: 16px;
    }

    .model-description {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 4px;
      border-left: 4px solid #2196f3;
    }
  }

  .dialog-footer {
    padding: 16px 24px;
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
  }
}
</style>
