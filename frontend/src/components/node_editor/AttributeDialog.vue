<template>
  <q-dialog
    v-model="editorStore.attributeDialog.visible"
    persistent
  >
    <q-card class="attribute-dialog">
      <q-card-section class="row dialog-header">
        <span>{{ editorStore.attributeDialog.title }}</span>
        <q-space />
        <q-btn
          flat
          dense
          round
          icon="close"
          @click="closeAttributeDialog"
        />
      </q-card-section>

      <q-card-section class="dialog-body">
        <!-- 基础属性配置 -->
        <div class="basic-attributes">
          <div class="section-title">基础配置</div>
          <div class="attribute-form">
            <q-input
              v-if="editorStore.attributeDialog.data.name !== undefined"
              v-model="editorStore.attributeDialog.data.name"
              label="模块名称"
              outlined
              dense
              class="form-field"
            />

            <q-input
              v-if="editorStore.attributeDialog.data.type"
              :model-value="getModuleTypeName(editorStore.attributeDialog.data.type)"
              label="模块类型"
              outlined
              dense
              readonly
              class="form-field"
            />

            <div
              v-if="ModuleConfig[editorStore.attributeDialog.data.type]?.supportProcessModel"
              class="form-field"
            >
              <q-select
                v-model="selectedProcessModel"
                :options="processModelOptions"
                label="进程模型"
                outlined
                dense
                emit-value
                map-options
                @update:model-value="onProcessModelChange"
              >
                <template v-slot:no-option>
                  <q-item>
                    <q-item-section class="text-grey">
                      暂无可用的进程模型
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>
          </div>
        </div>

        <q-separator class="section-separator" />

        <!-- 属性配置树 -->
        <div class="attribute-configuration">
          <div class="section-title">属性配置</div>
          <div class="attribute-tree-container">
            <div
              v-if="isLoadingAttributes"
              class="no-attributes-message"
            >
              <q-spinner size="md" class="q-mr-sm" />
              <span>正在加载模型属性...</span>
            </div>
            <div
              v-else-if="!currentProcessModelAttributes || currentProcessModelAttributes.length === 0"
              class="no-attributes-message"
            >
              <q-icon name="info" size="md" class="q-mr-sm" />
              <span v-if="!selectedProcessModel">请选择进程模型以配置相关属性</span>
              <span v-else>该模型暂无可配置属性</span>
            </div>
            <AttributeTree
              v-else
              :attributes="currentProcessModelAttributes"
              :values="editorStore.attributeDialog.data.attributeValues || {}"
              @update:values="onAttributeValuesUpdate"
            />
          </div>
        </div>
      </q-card-section>

      <q-card-actions class="dialog-footer" align="right">
        <q-btn
          flat
          label="取消"
          @click="closeAttributeDialog"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="applyAndCloseAttributes"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useEditorStore } from 'stores/editor'
import { getModuleTypeName, ModuleConfig } from 'src/utils/modules.js'
import { modelManager, PROC } from 'src/utils/modelManager.js'
import AttributeTree from './AttributeTree.vue'

const editorStore = useEditorStore()

// 当前选择的进程模型
const selectedProcessModel = ref('')

// 可用的进程模型列表
const availableModels = ref([])

// 加载状态
const isLoadingModels = ref(false)
const isLoadingAttributes = ref(false)

// 进程模型选项
const processModelOptions = computed(() => {
  const options = [{ label: '无', value: '' }]

  if (isLoadingModels.value) {
    options.push({ label: '正在加载...', value: '', disabled: true })
    return options
  }

  availableModels.value.forEach(model => {
    const modelName = modelManager.removeModelSuffix(model.name)
    options.push({
      label: `${modelName} (${model.visibility})`,
      value: modelName
    })
  })

  return options
})

// 当前进程模型的属性定义
const currentProcessModelAttributes = ref([])

// 加载进程模型的属性定义
const loadProcessModelAttributes = async (modelName) => {
  if (!modelName) {
    currentProcessModelAttributes.value = []
    return
  }

  isLoadingAttributes.value = true
  try {
    const modelData = await modelManager.getModel(PROC, modelName)
    currentProcessModelAttributes.value = modelData.attributes || []
  } catch (error) {
    console.error('加载进程模型失败:', error)
    currentProcessModelAttributes.value = []
    // 可以在这里添加用户友好的错误提示
  } finally {
    isLoadingAttributes.value = false
  }
}

// 监听对话框打开，初始化选择的进程模型
watch(() => editorStore.attributeDialog.visible, async (visible) => {
  if (visible) {
    // 加载可用的进程模型列表
    isLoadingModels.value = true
    try {
      availableModels.value = await modelManager.listModel(PROC)
    } catch (error) {
      console.error('加载进程模型列表失败:', error)
      availableModels.value = []
    } finally {
      isLoadingModels.value = false
    }

    // 初始化选择的进程模型
    selectedProcessModel.value = editorStore.attributeDialog.data.model || ''

    // 如果有选择的模型，加载其属性定义
    if (selectedProcessModel.value) {
      await loadProcessModelAttributes(selectedProcessModel.value)
    }
  }
})

const closeAttributeDialog = () => {
  editorStore.attributeDialog.visible = false
  editorStore.attributeDialog.data = {}
  editorStore.attributeDialog.node = null
}

const onProcessModelChange = async (modelName) => {
  editorStore.attributeDialog.data.model = modelName
  // 清空之前的属性值，因为不同模型的属性结构可能不同
  editorStore.attributeDialog.data.attributeValues = {}

  // 加载新模型的属性定义
  await loadProcessModelAttributes(modelName)
}

const onAttributeValuesUpdate = (newValues) => {
  editorStore.attributeDialog.data.attributeValues = newValues
}

const applyAndCloseAttributes = () => {
  // 应用属性到节点
  if (editorStore.attributeDialog.node) {
    editorStore.attributeDialog.node.label = editorStore.attributeDialog.data.name;
    editorStore.attributeDialog.node.setData(editorStore.attributeDialog.data);
    editorStore.hasUnsavedChanges = true;
  }
  closeAttributeDialog()
}


</script>

<style lang="scss" scoped>
.attribute-dialog {
  width: 800px;
  max-width: 90vw;
  max-height: 80vh;

  .dialog-header {
    padding-bottom: 0;
  }

  .dialog-body {
    max-height: 60vh;
    overflow-y: auto;
    padding-top: 0;

    .basic-attributes {
      margin-bottom: 0;

      .section-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
      }

      .attribute-form {
        .form-field {
          margin-bottom: 8px;
        }
      }
    }

    .section-separator {
      margin: 12px 0;
    }

    .attribute-configuration {
      .section-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 16px;
        color: #333;
      }

      .attribute-tree-container {
        min-height: 200px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 16px;
        background: #fafafa;

        .no-attributes-message {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100px;
          color: #666;
          font-style: italic;
        }
      }
    }
  }

  .dialog-footer {
    padding: 16px 24px;
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
  }
}
</style>
