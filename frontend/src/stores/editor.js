import { defineStore } from 'pinia'

export const useEditorStore = defineStore('editor', {
  state: () => ({
    statusMessage: '正在初始化...',
    connectionMode: 'packet_stream',
    cursorPosition: '0, 0',
    zoomLevel: 200,
    canUndo: false,
    canRedo: false,
    hasUnsavedChanges: false,
    stats: {
      moduleCount: 0,
      connectionCount: 0
    },
    attributeDialog: {
      visible: false,
      title: '属性编辑',
      data: {},
      node: null
    },
    processModelDialog: {
      visible: false,
      selectedModel: '',
      node: null
    },
    connectionDialog: {
      visible: false,
      title: '连接属性',
      data: {},
      edge: null,
      availableSourcePorts: [],
      availableTargetPorts: []
    },
    contextMenu: {
      visible: false,
      targetType: null, // 'node', 'edge', 'blank'
      targetCell: null,
      selectedCells: []
    },
  })
});


