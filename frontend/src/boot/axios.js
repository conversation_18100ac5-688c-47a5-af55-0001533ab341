import { defineBoot } from '#q-app/wrappers'
import axios from 'axios'

// 创建默认的axios实例，后续会根据主应用配置动态更新baseURL
const api = axios.create({
  baseURL: '',
  timeout: 30000,
  withCredentials: true,
})

// 用于存储插件配置的axios实例
let pluginApi = null

/**
 * 根据插件配置创建axios实例
 */
export const createPluginApi = (config) => {
  const { apiBase, token } = config

  pluginApi = axios.create({
    baseURL: apiBase,
    withCredentials: true,
  })

  // 如果有token，添加到请求头
  if (token) {
    pluginApi.defaults.headers.common['Authorization'] = `Bearer ${token}`
  }

  // 请求拦截器
  // pluginApi.interceptors.request.use(
  //   (config) => {
  //     console.log('发送API请求:', config.method?.toUpperCase(), config.url)
  //     return config
  //   },
  //   (error) => {
  //     console.error('请求拦截器错误:', error)
  //     return Promise.reject(error)
  //   }
  // )

  // 响应拦截器
  // pluginApi.interceptors.response.use(
  //   (response) => {
  //     console.log('收到API响应:', response.status, response.config.url)
  //     return response
  //   },
  //   (error) => {
  //     console.error('API请求失败:', error.response?.status, error.config?.url, error.message)
  //     return Promise.reject(error)
  //   }
  // )

  console.log('插件API实例已创建:', { apiBase, hasToken: !!token })
  return pluginApi
}

/**
 * 获取插件API实例
 */
export const getPluginApi = () => {
  if (!pluginApi) {
    console.warn('插件API实例尚未创建，请先调用createPluginApi')
  }
  return pluginApi
}

export default defineBoot(({ app }) => {
  // for use inside Vue files (Options API) through this.$axios and this.$api
  app.config.globalProperties.$axios = axios
  app.config.globalProperties.$api = api

  // 添加插件API到全局属性
  app.config.globalProperties.$pluginApi = () => getPluginApi()
})

export { api }
